name: 每日订阅状态维护

on:
  # 每日UTC时间02:00执行（避开用户活跃时间）
  schedule:
    - cron: '0 2 * * *'
  
  # 允许手动触发
  workflow_dispatch:
    inputs:
      env:
        description: '运行环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - 'staging'
          - 'production'
      dry_run:
        description: '是否为试运行模式（仅查询不更新）'
        required: false
        default: 'false'
        type: choice
        options:
          - 'false'
          - 'true'
      task_type:
        description: '执行的任务类型'
        required: false
        default: 'run-all'
        type: choice
        options:
          - 'run-all'
          - 'check-vip'
          - 'cleanup-expired'
          - 'cleanup-temp-entitlements'
          - 'cleanup-trials'
          - 'update-stats'

env:
  # 根据环境动态设置配置
  ENVIRONMENT: ${{ github.event_name == 'schedule' && 'production' || github.event.inputs.env || 'staging' }}
  SUPABASE_URL: ${{ github.event_name == 'schedule' && secrets.PROD_SUPABASE_URL || (github.event.inputs.env == 'production' && secrets.PROD_SUPABASE_URL || secrets.STAGING_SUPABASE_URL) }}
  CHECK_API_SECRET: ${{ github.event_name == 'schedule' && secrets.PROD_CHECK_API_SECRET || (github.event.inputs.env == 'production' && secrets.PROD_CHECK_API_SECRET || secrets.STAGING_CHECK_API_SECRET) }}
  SUPABASE_SERVICE_ROLE_KEY: ${{ github.event_name == 'schedule' && secrets.PROD_SUPABASE_SERVICE_ROLE_KEY || (github.event.inputs.env == 'production' && secrets.PROD_SUPABASE_SERVICE_ROLE_KEY || secrets.STAGING_SUPABASE_SERVICE_ROLE_KEY) }}

jobs:
  maintenance:
    name: 执行订阅状态维护
    runs-on: ubuntu-latest
    timeout-minutes: 15  # 15分钟超时
    
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
      
      - name: 设置运行变量
        id: setup
        run: |
          # 确定任务类型
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            TASK_TYPE="${{ github.event.inputs.task_type }}"
            DRY_RUN="${{ github.event.inputs.dry_run }}"
          else
            TASK_TYPE="run-all"
            DRY_RUN="false"
          fi
          
          echo "task_type=$TASK_TYPE" >> $GITHUB_OUTPUT
          echo "dry_run=$DRY_RUN" >> $GITHUB_OUTPUT
          echo "execution_id=${{ github.run_id }}" >> $GITHUB_OUTPUT
          
          # 构建API URL
          API_URL="${SUPABASE_URL}/functions/v1/revenuecat-check/$TASK_TYPE"
          echo "api_url=$API_URL" >> $GITHUB_OUTPUT
          
          echo "🔧 任务配置:"
          echo "  环境: $ENVIRONMENT"
          echo "  任务类型: $TASK_TYPE"
          echo "  试运行: $DRY_RUN"
          echo "  执行ID: ${{ github.run_id }}"
          echo "  API URL: $API_URL"
      
      - name: 检查API密钥配置
        run: |
          if [[ -z "${SUPABASE_URL}" ]]; then
            echo "❌ 错误: 未设置 SUPABASE_URL"
            exit 1
          fi
          
          if [[ -z "${CHECK_API_SECRET}" ]]; then
            echo "❌ 错误: 未设置 CHECK_API_SECRET"
            exit 1
          fi
          
          echo "✅ API配置检查通过"
      
      # - name: 记录任务开始
      #   id: start_log
      #   run: |
      #     echo "📝 记录维护任务开始..."
      #     
      #     # 调用Supabase函数记录任务开始
      #     LOG_RESPONSE=$(curl -s -X POST "${SUPABASE_URL}/rest/v1/rpc/start_maintenance_task" \
      #       -H "Authorization: Bearer ${SUPABASE_SERVICE_ROLE_KEY}" \
      #       -H "apikey: ${SUPABASE_SERVICE_ROLE_KEY}" \
      #       -H "Content-Type: application/json" \
      #       -d '{
      #         "p_task_type": "${{ steps.setup.outputs.task_type }}",
      #         "p_triggered_by": "github_actions",
      #         "p_execution_id": "${{ steps.setup.outputs.execution_id }}"
      #       }' || echo "")
      #     
      #     if [[ -n "$LOG_RESPONSE" ]]; then
      #       LOG_ID=$(echo "$LOG_RESPONSE" | jq -r '.' 2>/dev/null || echo "")
      #       echo "log_id=$LOG_ID" >> $GITHUB_OUTPUT
      #       echo "✅ 任务日志记录成功，ID: $LOG_ID"
      #     else
      #       echo "⚠️ 任务日志记录失败，继续执行"
      #       echo "log_id=" >> $GITHUB_OUTPUT
      #     fi
      
      - name: 执行维护任务
        id: maintenance
        run: |
          echo "🚀 开始执行维护任务..."
          
          # 构建请求体
          REQUEST_BODY='{"dry_run": ${{ steps.setup.outputs.dry_run }}}'
          
          echo "📡 调用API: ${{ steps.setup.outputs.api_url }}"
          echo "📄 请求体: $REQUEST_BODY"
          
          # 执行维护任务
          RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
            -X POST "${{ steps.setup.outputs.api_url }}" \
            -H "X-Check-Api-Secret: ${CHECK_API_SECRET}" \
            -H "Authorization: Bearer ${CHECK_API_SECRET}" \
            -H "apikey: ${SUPABASE_SERVICE_ROLE_KEY}" \
            -H "Content-Type: application/json" \
            -d "$REQUEST_BODY")
          
          # 解析响应
          HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
          RESPONSE_BODY=$(echo "$RESPONSE" | sed '/HTTP_STATUS:/d')
          
          echo "📊 HTTP状态码: $HTTP_STATUS"
          echo "📋 响应内容:"
          echo "$RESPONSE_BODY" | jq '.' 2>/dev/null || echo "$RESPONSE_BODY"
          
          # 检查执行结果
          if [[ "$HTTP_STATUS" == "200" ]]; then
            SUCCESS=$(echo "$RESPONSE_BODY" | jq -r '.success' 2>/dev/null || echo "false")
            if [[ "$SUCCESS" == "true" ]]; then
              echo "status=success" >> $GITHUB_OUTPUT
              echo "✅ 维护任务执行成功"
            else
              echo "status=failed" >> $GITHUB_OUTPUT
              echo "❌ 维护任务执行失败"
            fi
          else
            echo "status=failed" >> $GITHUB_OUTPUT
            echo "❌ API调用失败，HTTP状态码: $HTTP_STATUS"
          fi
          
          # 保存完整响应用于后续步骤
          echo "response_body<<EOF" >> $GITHUB_OUTPUT
          echo "$RESPONSE_BODY" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
          echo "http_status=$HTTP_STATUS" >> $GITHUB_OUTPUT
      
      - name: 解析执行结果
        id: parse_result
        if: always()
        run: |
          echo "📊 解析执行结果..."
          
          RESPONSE='${{ steps.maintenance.outputs.response_body }}'
          
          if [[ -n "$RESPONSE" ]] && echo "$RESPONSE" | jq '.' > /dev/null 2>&1; then
            # 解析成功响应
            USERS_CHECKED=0
            USERS_UPDATED=0
            SUBSCRIPTIONS_CLEANED=0
            TEMP_ENTITLEMENTS_CLEANED=0
            TRIALS_CLEANED=0
            
            if [[ "${{ steps.setup.outputs.task_type }}" == "run-all" ]]; then
              # 解析run-all的复合结果
              USERS_CHECKED=$(echo "$RESPONSE" | jq -r '.result.vip_status_check.total_users_checked // 0' 2>/dev/null || echo "0")
              USERS_UPDATED=$(echo "$RESPONSE" | jq -r '.result.vip_status_check.users_to_update // 0' 2>/dev/null || echo "0")
              SUBSCRIPTIONS_CLEANED=$(echo "$RESPONSE" | jq -r '.result.expired_cleanup.expired_subscriptions // 0' 2>/dev/null || echo "0")
              TEMP_ENTITLEMENTS_CLEANED=$(echo "$RESPONSE" | jq -r '.result.temp_entitlements_cleanup.expired_entitlements // 0' 2>/dev/null || echo "0")
              TRIALS_CLEANED=$(echo "$RESPONSE" | jq -r '.result.trials_cleanup.expired_trials // 0' 2>/dev/null || echo "0")
            else
              # 解析单个任务结果
              USERS_CHECKED=$(echo "$RESPONSE" | jq -r '.result.total_users_checked // 0' 2>/dev/null || echo "0")
              USERS_UPDATED=$(echo "$RESPONSE" | jq -r '.result.users_to_update // 0' 2>/dev/null || echo "0")
            fi
            
            echo "users_checked=$USERS_CHECKED" >> $GITHUB_OUTPUT
            echo "users_updated=$USERS_UPDATED" >> $GITHUB_OUTPUT
            echo "subscriptions_cleaned=$SUBSCRIPTIONS_CLEANED" >> $GITHUB_OUTPUT
            echo "temp_entitlements_cleaned=$TEMP_ENTITLEMENTS_CLEANED" >> $GITHUB_OUTPUT
            echo "trials_cleaned=$TRIALS_CLEANED" >> $GITHUB_OUTPUT
            
            echo "📈 执行统计:"
            echo "  检查用户数: $USERS_CHECKED"
            echo "  更新用户数: $USERS_UPDATED"
            echo "  清理订阅数: $SUBSCRIPTIONS_CLEANED"
            echo "  清理临时授权数: $TEMP_ENTITLEMENTS_CLEANED"
            echo "  清理试用期数: $TRIALS_CLEANED"
          else
            echo "⚠️ 无法解析响应内容"
            echo "users_checked=0" >> $GITHUB_OUTPUT
            echo "users_updated=0" >> $GITHUB_OUTPUT
            echo "subscriptions_cleaned=0" >> $GITHUB_OUTPUT
            echo "temp_entitlements_cleaned=0" >> $GITHUB_OUTPUT
            echo "trials_cleaned=0" >> $GITHUB_OUTPUT
          fi
      
      # - name: 更新任务日志
      #   if: always() && steps.start_log.outputs.log_id != ''
      #   run: |
      #     echo "📝 更新维护任务日志..."
      #     
      #     LOG_ID="${{ steps.start_log.outputs.log_id }}"
      #     STATUS="${{ steps.maintenance.outputs.status }}"
      #     
      #     # 构建更新请求
      #     UPDATE_BODY=$(cat << EOF
      #     {
      #       "p_log_id": "$LOG_ID",
      #       "p_status": "$STATUS",
      #       "p_users_checked": ${{ steps.parse_result.outputs.users_checked }},
      #       "p_users_updated": ${{ steps.parse_result.outputs.users_updated }},
      #       "p_subscriptions_cleaned": ${{ steps.parse_result.outputs.subscriptions_cleaned }},
      #       "p_temp_entitlements_cleaned": ${{ steps.parse_result.outputs.temp_entitlements_cleaned }},
      #       "p_trials_cleaned": ${{ steps.parse_result.outputs.trials_cleaned }},
      #       "p_execution_result": ${{ steps.maintenance.outputs.response_body || 'null' }},
      #       "p_error_message": $(if [[ "$STATUS" == "failed" ]]; then echo "\"HTTP ${{ steps.maintenance.outputs.http_status }}: 执行失败\""; else echo "null"; fi)
      #     }
      #     EOF
      #     )
      #     
      #     # 更新日志
      #     curl -s -X POST "${SUPABASE_URL}/rest/v1/rpc/complete_maintenance_task" \
      #       -H "Authorization: Bearer ${CHECK_API_SECRET}" \
      #       -H "Content-Type: application/json" \
      #       -H "apikey: ${CHECK_API_SECRET}" \
      #       -d "$UPDATE_BODY" > /dev/null || echo "⚠️ 日志更新失败"
      #     
      #     echo "✅ 任务日志更新完成"
      
      - name: 生成执行摘要
        if: always()
        run: |
          echo "## 📊 维护任务执行摘要" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| 项目 | 值 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| 执行时间 | $(date -u '+%Y-%m-%d %H:%M:%S UTC') |" >> $GITHUB_STEP_SUMMARY
          echo "| 任务类型 | ${{ steps.setup.outputs.task_type }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 执行模式 | $(if [[ '${{ steps.setup.outputs.dry_run }}' == 'true' ]]; then echo '试运行'; else echo '正式执行'; fi) |" >> $GITHUB_STEP_SUMMARY
          echo "| 执行状态 | $(if [[ '${{ steps.maintenance.outputs.status }}' == 'success' ]]; then echo '✅ 成功'; else echo '❌ 失败'; fi) |" >> $GITHUB_STEP_SUMMARY
          echo "| HTTP状态 | ${{ steps.maintenance.outputs.http_status }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 检查用户数 | ${{ steps.parse_result.outputs.users_checked }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 更新用户数 | ${{ steps.parse_result.outputs.users_updated }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 清理记录数 | $((${{ steps.parse_result.outputs.subscriptions_cleaned }} + ${{ steps.parse_result.outputs.temp_entitlements_cleaned }} + ${{ steps.parse_result.outputs.trials_cleaned }})) |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [[ "${{ steps.maintenance.outputs.status }}" == "failed" ]]; then
            echo "### ❌ 错误信息" >> $GITHUB_STEP_SUMMARY
            echo '```' >> $GITHUB_STEP_SUMMARY
            echo '${{ steps.maintenance.outputs.response_body }}' >> $GITHUB_STEP_SUMMARY
            echo '```' >> $GITHUB_STEP_SUMMARY
          fi
      
      - name: 检查执行结果并发送通知
        if: always()
        run: |
          # 设置默认值，避免变量为空时的计算错误
          USERS_CHECKED="${{ steps.parse_result.outputs.users_checked }}"
          USERS_UPDATED="${{ steps.parse_result.outputs.users_updated }}"
          SUBSCRIPTIONS_CLEANED="${{ steps.parse_result.outputs.subscriptions_cleaned }}"
          TEMP_ENTITLEMENTS_CLEANED="${{ steps.parse_result.outputs.temp_entitlements_cleaned }}"
          TRIALS_CLEANED="${{ steps.parse_result.outputs.trials_cleaned }}"
          
          # 设置默认值为0
          USERS_CHECKED=${USERS_CHECKED:-0}
          USERS_UPDATED=${USERS_UPDATED:-0}
          SUBSCRIPTIONS_CLEANED=${SUBSCRIPTIONS_CLEANED:-0}
          TEMP_ENTITLEMENTS_CLEANED=${TEMP_ENTITLEMENTS_CLEANED:-0}
          TRIALS_CLEANED=${TRIALS_CLEANED:-0}
          
          # 计算更新总数
          UPDATED_COUNT=$((USERS_UPDATED + SUBSCRIPTIONS_CLEANED + TEMP_ENTITLEMENTS_CLEANED + TRIALS_CLEANED))
          CLEANED_COUNT=$((SUBSCRIPTIONS_CLEANED + TEMP_ENTITLEMENTS_CLEANED + TRIALS_CLEANED))
          
          # 准备通知内容
          if [[ "${{ steps.maintenance.outputs.status }}" == "failed" ]]; then
            echo "❌ 维护任务执行失败"
            NOTIFICATION_TITLE="### ⚠️ 知我AI输入法 - 订阅维护任务失败"
            NOTIFICATION_CONTENT=">**任务类型**: ${{ steps.setup.outputs.task_type }}\n>**执行ID**: ${{ steps.setup.outputs.execution_id }}\n>**错误信息**: HTTP ${{ steps.maintenance.outputs.http_status }}\n>**环境**: ${{ env.ENVIRONMENT }}\n>**时间**: $(date -u "+%Y-%m-%d %H:%M:%S UTC")"
          else
            echo "✅ 维护任务执行成功"
            NOTIFICATION_TITLE="### ✅ 知我AI输入法 - 订阅维护任务完成"
            NOTIFICATION_CONTENT=">**任务类型**: ${{ steps.setup.outputs.task_type }}\n>**执行模式**: $(if [[ '${{ steps.setup.outputs.dry_run }}' == 'true' ]]; then echo '试运行'; else echo '正式执行'; fi)\n>**检查用户数**: ${USERS_CHECKED}\n>**更新用户数**: ${USERS_UPDATED}\n>**清理记录数**: ${CLEANED_COUNT}\n>**环境**: ${{ env.ENVIRONMENT }}\n>**时间**: $(date -u "+%Y-%m-%d %H:%M:%S UTC")"
            
            # 如果更新数量较多，添加警告信息
            if [[ $UPDATED_COUNT -gt 10 ]]; then
              echo "⚠️ 发现较多数据不一致($UPDATED_COUNT个)，建议检查"
              NOTIFICATION_TITLE="### ⚠️ 知我AI输入法 - 维护任务发现较多数据不一致"
              NOTIFICATION_CONTENT="${NOTIFICATION_CONTENT}\n>**警告**: 发现较多数据不一致(${UPDATED_COUNT}个)\n>**建议**: 请检查RevenueCat webhook配置"
            fi
          fi
          
          # 发送通知
          if [[ -n "${{ secrets.NOTIFICATION_WEBHOOK }}" ]]; then
            curl -s -X POST "${{ secrets.NOTIFICATION_WEBHOOK }}" \
              -H "Content-Type: application/json" \
              -d '{
                "msgtype": "markdown",
                "markdown": {
                  "content": "'"${NOTIFICATION_TITLE}"'\n'"${NOTIFICATION_CONTENT}"'"
                }
              }' || echo "通知发送失败"
          else
            echo "未配置通知Webhook，跳过通知发送"
          fi
          
          # 如果任务失败，退出并返回错误码
          if [[ "${{ steps.maintenance.outputs.status }}" == "failed" ]]; then
            exit 1
          fi