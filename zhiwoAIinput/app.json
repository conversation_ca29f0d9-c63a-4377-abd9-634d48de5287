{"expo": {"name": "KnowmeType", "slug": "knowmetype", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "knowmetype", "userInterfaceStyle": "automatic", "newArchEnabled": true, "locales": {"zh-Hans": "./locales/zh-Hans/infoPlistTranslation.json", "zh-Hant": "./locales/zh-Hant/infoPlistTranslation.json", "en": "./locales/en/infoPlistTranslation.json"}, "ios": {"supportsTablet": false, "bundleIdentifier": "com.mindpowerhk.knowmetype", "associatedDomains": ["applinks:npvxihqltugwsrnrbzgy.supabase.co"], "infoPlist": {"CFBundleDisplayName": "$(CFBundleDisplayName)", "ITSAppUsesNonExemptEncryption": false, "CFBundleAllowMixedLocalizations": true, "CFBundleDevelopmentRegion": "en", "CFBundleLocalizations": ["en", "zh-Hans", "zh-Han<PERSON>"]}, "buildNumber": "8"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.INTERNET", "android.permission.MODIFY_AUDIO_SETTINGS"], "package": "com.mindpowerhk.knowmetype", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "npvxihqltugwsrnrbzgy.supabase.co", "pathPrefix": "/"}, {"scheme": "knowmetype", "host": "*"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-splash-screen", ["expo-speech-recognition", {"microphonePermission": "允许应用访问您的麦克风进行语音识别", "speechRecognitionPermission": "允许应用进行语音识别"}], ["expo-av", {"microphonePermission": "需要访问麦克风来进行语音识别和录音"}]], "experiments": {"typedRoutes": true}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/882a46a1-9f61-423d-9d58-f5f653564a28"}}}