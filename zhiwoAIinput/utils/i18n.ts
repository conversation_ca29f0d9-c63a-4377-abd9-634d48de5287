import i18n, { i18n as I18n, Resource } from 'i18next';

export type I18nType = I18n;
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveLanguage, getLanguage as getStoredLanguage } from './languageStorage';
import { warn } from '@/services/logService';

// 导入语言资源
import en from '../locales/en/translation.json';
import zhHans from '../locales/zh-Hans/translation.json';
import zhHant from '../locales/zh-Hant/translation.json';

// 定义资源类型
export type TranslationResources = {
  loginGuide: {
    title: string;
    desc: string;
    skip: string;
    login: string;
  };
  common: Record<string, string>;
  home: {
    regenerateFailedNoTextOrTemplate: string;
    regenerateFailedRetry: string;
    apiRequestFailed: string;
    requestFailed: string;
    optimizationFailed: string;
    optimizationFailedRetry: string;
    whisperModelEnabled: string;
    title: string;
    inputTitle: string;
    inputSubtitle: string;
    startRecording: string;
    stopRecording: string;
    processing: string;
    optimize: string;
    copy: string;
    share: string;
    clear: string;
    selectTemplate: string;
    noTemplate: string;
    optimizedText: string;
    originalText: string;
    editHint: string;
    recording: string;
    transcribing: string;
    selectStyle: string;
    optimizationResult: string;
    aiOptimizing: string;
    vipExclusive: string;
    vipHint: string;
    regenerate: string;
    selectTemplateHint: string;
    moreStyles: string;
    noVoiceDetected: string;
    recordingError: string;
    recordingErrorMsg: string;
    recordingTimeout: string;
    recordingTimeoutMsg: string;
    stopRecordingBtn: string;
    continueRecordingBtn: string;
    speakNow: string;
    tapToPause: string;
    clickToStartRecording: string;
    recordingHint: string;
    inputOrEditText: string;
    newVoiceInput: string;
    finish: string;
    pause: string;
    continue: string;
    newInput: string;
    upgradeNow: string;
    upgradeToVIP: string;
    vipUpgradeDescription: string;
    optimizingText: string;
    retryFailed: string;
    audioFileCorrupted: string;
    audioFileNotFound: string;
    retryTranscriptionFailed: string;
    retryTranscriptionFailedMsg: string;
    retryFailedWithError: string;
    featureTags: {
      smartReworking: string;
      sceneAdaptation: string;
      personalizedTemplates: string;
    };
    textDisplay: Record<string, string>;
    networkError: Record<string, string>;
    whisperLimit: {
      loginPrompt: string;
      switchToNative: string;
      title: string;
      message: string;
      upgradeOption: string;
      switchOption: string;
      remainingCount: string;
      remainingCountDesc: string;
    };
  };
  history: {
    detail: {
      sourceRecordDeleted: string;
      sourceRecordDeletedDesc: string;
      title: string;
      backToList: string;
      originalContent: string;
      optimizedContent: string;
      optimizationSuccess: string;
      optimizationSuccessDesc: string;
      optimizationFailed: string;
      optimizationFailedDesc: string;
      jumpToResult: string;
      reoptimize: string;
      copySpecificResult: string;
      share: string;
      noOriginalContent: string;
      noOptimizedContent: string;
      optimizationHistory: string;
      copy: string;
      reoptimizedInfo: {
        title: string;
        description: string;
        viewOriginal: string;
        titleSimple: string;
        viewOriginalSimple: string;
      };
      sections: {
        originalAudio: string;
        aiOptimized: string;
      };
      actions: {
        copyText: string;
        reoptimize: string;
        shareResult: string;
      };
      templateModal: {
        title: string;
        sourceText: string;
        optimizedText: string;
        originalText: string;
        optimizeButton: string;
        cancelButton: string;
        optimizing: string;
        selectTemplate: string;
        startOptimize: string;
      };
      toast: {
        copySuccess: string;
        selectSingleResult: string;
        optimizationSuccess: string;
        optimizationSuccessMessage: string;
        optimizationFailed: string;
        optimizationFailedMessage: string;
        viewHistoryList: string;
      };
      errors: {
        templateNotFound: string;
      };
    };
    today: string;
    yesterday: string;
    dayBeforeYesterday: string;
    thisWeek: string;
    thisMonth: string;
    earlier: string;
    expand: string;
    collapse: string;
    share: string;
    delete: string;
    copy: string;
    all: string;
    workRelated: string;
    email: string;
    meetingNotes: string;
    chat: string;
    important: string;
    personal: string;
    draft: string;
    archived: string;
    title: string;
    noRecords: string;
    deleteConfirm: string;
    deleteConfirmMultiple: string;
    deleteConfirmTitle: string;
    deleteSuccess: string;
    deleteSuccessMultiple: string;
    copySuccess: string;
    shareSuccess: string;
    searchPlaceholder: string;
    selectAll: string;
    deselectAll: string;
    select: string;
    cancel: string;
    vipAllRecords: string;
    freeUserLimitedRecords: string;
    freeUserAllRecords: string;
    deleteSelected: string;
    noRecordsFound: string;
    loadMore: string;
    loading: string;
    pullToRefresh: string;
    releaseToRefresh: string;
    refreshing: string;
    emptyStateTitle: string;
    emptyStateSubtitle: string;
    startRecording: string;
    selectMode: string;
    selectedCount: string;
    vipUpgrade: {
      title: string;
      subtitle: string;
      upgradeButton: string;
    };
  };
  settings: {
    appName: string;
    languageChangeFailed: string;
    wechatLogin: string;
    appleLogin: string;
    googleLogin: string;
    emailLogin: string;
    phoneLogin: string;
    title: string;
    account: string;
    selectTranscribeModel: string;
    selectLanguageModel: string;
    uiLanguage: string;
    selectLanguage: string;
    themeStyle: string;
    selectThemeStyle: string;
    themeSystemStyle: string;
    themeSystemStyleDesc: string;
    themeDarkStyle: string;
    themeDarkStyleDesc: string;
    themeLightStyle: string;
    themeLightStyleDesc: string;
    fastMode: string;
    fastModeDescription: string;
    fastModeDisabledDescription: string;
    styleOptimization: string;
    styleOptimizationDescription: string;
    styleTemplate: string;
    templatePersonalization: string;
    templatePersonalizationSubtitle: string;
    templatePersonalizationPortal: string;
    templateManagerInstructions: string;
    templateManagerLoading: string;
    templateManagerSaving: string;
    templateManagerLoadError: string;
    templateManagerSaveError: string;
    templateManagerSystemTag: string;
    templateManagerCustomTag: string;
    templateManagerDefaultTag: string;
    templateManagerDeletedTemplate: string;
    templateManagerDeletedTemplateDesc: string;
    templateManagerOrderExplanation: string;
    templateManagerDragHint: string;
    transcribeModel: string;
    languageModel: string;
    theme: string;
    about: string;
    feedback: string;
    rateUs: string;
    rateUsDescription: string;
    rateInAppStore: string;
    rateInGooglePlay: string;
    thankYouForRating: string;
    ratingNotAvailable: string;
    openStoreError: string;
    privacyPolicy: string;
    termsOfService: string;
    logout: string;
    logoutConfirm: string;
    switchingLanguage: string;
    deleteAllData: string;
    dataManagement: string;
    deleteAllDataConfirm: string;
    deleteAllDataSuccess: string;
    deleteAllDataSuccessDesc: string;
    version: string;
    versionUpdate: string;
    vipExclusiveFeature: string;
    onlyVIPUsersCanUseThisModel: string;
    upgradeToUseVipModel: string;
    upgradeNow: string;
    vipBadgeText: string;
    vipCardTitle: string;
    vipCardSubtitle: string;
    unlimitedCustomTemplate: string;
    advancedAIModel: string;
    priorityRequest: string;
    fullHistoryRecord: string;
    vipSubscribed: string;
    checkVipDetail: string;
    vipSubscribedCardTitle: string;
    vipSubscribedCardSubtitle: string;
    loginGuideTitle: string;
    loginGuideSubtitle: string;
    loginGuideButtonText: string;
    toast: {
      settingsSaved: string;
      settingsSaveSuccess: string;
      settingsSaveFailed: string;
      localSettingsSaveFailed: string;
      cloudSyncFailed: string;
      settingsSavedWithIssue: string;
      languageChangeFailed: string;
    };
    vipFeatures: {
      title: string;
      message: string;
      aiModelSettingTitle: string;
      aiModelSettingMessage: string;
    };
    languageNames: Record<string, string>;
    subscriptionPlans: {
      monthly: string;
      yearly: string;
      vip: string;
    };
    authProviders: {
      unknown: string;
    };
    validityInfo: {
      notAvailable: string;
    };
    models: {
      nativeTranscribe: {
        ios: string;
        android: string;
      };
    };
    prompts: {
      upgradeVipTitle: string;
      upgradeVipMessage: string;
      modelSettingsTitle: string;
      modelSettingsMessage: string;
    };
    feedbackEmailSubject: string;
    feedbackEmailBodyLineX: string;
  };
  dataManagement: Record<string, string>;
  tabs: {
    input: string;
    history: string;
    settings: string;
  };
  templates: Record<string, string>;
  errors: Record<string, string>;
  auth: Record<string, string>;
  vipDetail: {
    title: string;
    vipStatus: string;
    validUntil: string;
    featuresTitle: string;
    changePlan: string;
    validityNotAvailable: string;
    plans: {
      monthly: string;
      yearly: string;
      default: string;
    };
    features: {
      unlimitedTemplates: {
        title: string;
        description: string;
      };
      advancedModel: {
        title: string;
        description: string;
      };
      priorityProcessing: {
        title: string;
        description: string;
      };
      cloudSync: {
        title: string;
        description: string;  
      };
    };
  };
  payment: {
    currentPackage: string;
    title: string;
    priceHint: string;
    getVip: string;
    unlockPremium: string;
    award2025: string;
    awardApp: string;
    vipBenefits: string;
    userReviews: string;
    subscriptionOptions: string;
    monthly: string;
    yearly: string;
    billedAs: string;
    trialBilling: string;
    subscribeNow: string;
    startTrial: string;
    restorePurchases: string;
    termsOfService: string;
    privacyPolicy: string;
    subscriptionTerms: string;
    subscriptionTermsIos: string;
    subscriptionTermsAndroid: string;
    paymentInfoIos: string;
    paymentInfoAndroid: string;
    benefits: {
      unlimitedTemplates: {
        title: string;
        description: string;
      };
      advancedModel: {
        title: string;
        description: string;
      };
      priorityRequest: {
        title: string;
        description: string;
      };
      cloudSync: {
        title: string;
        description: string;
      };
    };
    reviews: Record<string, {
      title: string;
      content: string;
      author: string;
    }>;
    errors: {
      purchaseFailed: string;
      purchaseError: string;
      restoreFailed: string;
      restoreError: string;
      noSubscriptionFound: string;
      noActiveSubscription: string;
      loginRequired: string;
      loginRequiredMessage: string;
      subscriptionError: string;
      noSubscriptionAvailable: string;
    };
    success: {
      purchaseSuccess: string;
      purchaseSuccessMessage: string;
      restoreSuccess: string;
      restoreSuccessMessage: string;
    };
    loading: {
      loading: string;
      processing: string;
    };
  };
  login: Record<string, string>;
  aiModels: Record<string, {
    name: string;
    description: string;
  }>;
  template: Record<string, {
    name: string;
    description: string;
  }>;
  onboarding: {
    languageSelection: {
      title: string;
      subtitle: string;
    };
    welcome: {
      title: string;
      subtitle: string;
      changeLanguage: string;
    };
    source: {
      title: string;
      options: Record<string, string>;
    };
    useCases: {
      title: string;
      subtitle: string;
      options: Record<string, string>;
    };
    templates: {
      title: string;
      subtitle: string;
      vipRequired: string;
      loadError: string;
      loadErrorDesc: string;
      noTemplates: string;
      loading: string;
    };
    navigation: {
      next: string;
      back: string;
      complete: string;
      saving: string;
      skip: string;
    };
    progress: string;
  };
};
// export type TranslationResources = Record<string, any>;

// 语言资源
const resources = {
  en: {
    translation: en,
  },
  'zh-Hans': {
    translation: zhHans,
  },
  'zh-Hant': {
    translation: zhHant,
  },
};

// 语言代码映射
const languageMap: Record<string, string> = {
  en: 'en',
  'zh-Hans': 'zh-Hans',
  'zh-CN': 'zh-Hans',
  'zh-Hant': 'zh-Hant',
  'zh-TW': 'zh-Hant',
  'zh-HK': 'zh-Hant',
};

// 获取设备语言
export const getDeviceLanguage = (): string => {
  try {
    const locale = Localization.locale;
    return languageMap[locale] || 'en';
  } catch (error) {
    warn('Failed to get device locale, using default (en)');
    return 'en';
  }
};

// 声明i18n类型
declare module 'i18next' {
  interface CustomTypeOptions {
    defaultNS: 'translation';
    resources: {
      translation: TranslationResources;
    };
  }
}

// 初始化i18n
export const initI18n = async (): Promise<I18n> => {
  // 使用 languageStorage 中的函数获取语言
  const savedLanguage = await getStoredLanguage();
  const deviceLanguage = getDeviceLanguage();
  const languageToUse = savedLanguage || deviceLanguage;

  const i18nInstance = i18n.createInstance() as I18n;

  await i18nInstance.use(initReactI18next).init({
    resources,
    lng: languageToUse,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    compatibilityJSON: 'v3',
  });

  // 监听语言变化
  i18nInstance.on('languageChanged', async (lng) => {
    // 当语言变化时，保存到存储
    await saveLanguage(lng as any);
  });

  // 设置全局实例（为了向后兼容）
  if (!i18n.isInitialized) {
    await i18n.init({
      resources,
      lng: languageToUse,
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false,
      },
      compatibilityJSON: 'v3',
    });
  }

  return i18nInstance;
};

// 切换语言
export const changeLanguage = async (language: string): Promise<void> => {
  if (i18n.isInitialized) {
    await i18n.changeLanguage(language);
  }
  await AsyncStorage.setItem('userLanguage', language);
};

export default i18n;
